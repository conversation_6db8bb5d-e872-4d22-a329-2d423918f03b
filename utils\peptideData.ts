import { Peptide } from '@/types/peptide';
import peptidesData from '../toptier_peptides.json';

export const peptides: Peptide[] = peptidesData as Peptide[];

// Extract unique categories
export const categories = Array.from(new Set(peptides.map(p => p.category)));

// Extract common keywords from uses and effects
export const extractKeywords = (text: string): string[] => {
  const commonWords = ['and', 'or', 'the', 'in', 'for', 'with', 'to', 'of', 'from', 'by', 'as', 'is', 'are', 'may', 'can', 'also', 'often', 'used'];
  return text
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 3 && !commonWords.includes(word))
    .slice(0, 10); // Limit to top 10 keywords
};

// Get peptides by category
export const getPeptidesByCategory = (category: string): Peptide[] => {
  return peptides.filter(p => p.category === category);
};

// Search peptides by keywords
export const searchPeptides = (keywords: string[]): Peptide[] => {
  if (!keywords.length) return [];
  
  return peptides.filter(peptide => {
    const peptideText = `${peptide.uses} ${peptide.effects}`.toLowerCase();
    return keywords.some(keyword => 
      peptideText.includes(keyword.toLowerCase())
    );
  });
};

// Get time frame preference
export const getTimeFrameWeeks = (timeFrame: string): number => {
  const match = timeFrame.match(/(\d+)[-–](\d+)\s*weeks?/i);
  if (match) {
    return (parseInt(match[1]) + parseInt(match[2])) / 2;
  }
  
  const singleMatch = timeFrame.match(/(\d+)\s*weeks?/i);
  if (singleMatch) {
    return parseInt(singleMatch[1]);
  }
  
  // Default to 4 weeks if no match
  return 4;
};
