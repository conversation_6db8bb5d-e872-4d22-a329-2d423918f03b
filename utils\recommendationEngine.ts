import { Peptide, QuizResponse, PeptideRecommendation, QuizResult } from '@/types/peptide';
import { peptides, getTimeFrameWeeks } from './peptideData';

export const generateRecommendations = (responses: QuizResponse[]): QuizResult => {
  // Collect all keywords and weights from responses
  const userKeywords: { keyword: string; weight: number }[] = [];
  const userCategories: string[] = [];
  const primaryGoals: string[] = [];
  
  responses.forEach(response => {
    response.keywords.forEach(keyword => {
      userKeywords.push({ keyword, weight: response.weight });
    });
  });

  // Determine time preference
  const timeResponse = responses.find(r => r.questionId === 'time-preference');
  let preferredTimeFrame = 'medium-term';
  if (timeResponse) {
    preferredTimeFrame = timeResponse.selectedOptionId;
  }

  // Score each peptide
  const scoredPeptides: PeptideRecommendation[] = peptides.map(peptide => {
    let score = 0;
    const matchedKeywords: string[] = [];
    const reasons: string[] = [];

    const peptideText = `${peptide.uses} ${peptide.effects} ${peptide.category}`.toLowerCase();
    
    // Score based on keyword matches
    userKeywords.forEach(({ keyword, weight }) => {
      if (peptideText.includes(keyword.toLowerCase())) {
        score += weight;
        matchedKeywords.push(keyword);
      }
    });

    // Bonus for category matches
    const categoryBonus = getCategoryBonus(peptide.category, responses);
    score += categoryBonus;

    // Time frame preference bonus
    const timeBonus = getTimeFrameBonus(peptide.time_frame, preferredTimeFrame);
    score += timeBonus;

    // Generate reasons
    if (matchedKeywords.length > 0) {
      reasons.push(`Matches your goals: ${matchedKeywords.slice(0, 3).join(', ')}`);
    }
    
    if (categoryBonus > 0) {
      reasons.push(`Fits your focus area: ${peptide.category}`);
    }

    if (timeBonus > 0) {
      reasons.push(`Results timeline: ${peptide.time_frame}`);
    }

    return {
      peptide,
      score,
      matchedKeywords,
      reasons
    };
  });

  // Sort by score and take top recommendations
  const recommendations = scoredPeptides
    .filter(rec => rec.score > 0)
    .sort((a, b) => b.score - a.score)
    .slice(0, 5);

  // Extract user profile
  const primaryGoalResponse = responses.find(r => r.questionId === 'primary-goal');
  if (primaryGoalResponse) {
    primaryGoals.push(primaryGoalResponse.selectedOptionId);
  }

  return {
    recommendations,
    userProfile: {
      primaryGoals,
      timePreference: preferredTimeFrame,
      categories: userCategories
    }
  };
};

const getCategoryBonus = (peptideCategory: string, responses: QuizResponse[]): number => {
  const categoryMap: { [key: string]: string[] } = {
    'Weight Loss and Metabolism': ['weight-loss', 'stubborn-fat'],
    'Growth Hormone/Body Composition/Performance': ['muscle-performance', 'very-active', 'physical-performance'],
    'Healing and Recovery': ['healing-recovery', 'joint-pain', 'recovery-healing'],
    'Cognition & Mood': ['cognitive-mood', 'brain-fog', 'mental-performance'],
    'Longevity/Mitochondrial Health/Energy': ['longevity-energy', 'low-energy', 'overall-wellness'],
    'Sleep': ['sleep-quality', 'poor-sleep'],
    'Immunity': ['immune-issues'],
    'Sexual Health': ['sexual-health']
  };

  const userSelections = responses.map(r => r.selectedOptionId);
  const relevantSelections = categoryMap[peptideCategory] || [];
  
  const matches = relevantSelections.filter(selection => 
    userSelections.includes(selection)
  );

  return matches.length * 15; // 15 points per category match
};

const getTimeFrameBonus = (peptideTimeFrame: string, userPreference: string): number => {
  const peptideWeeks = getTimeFrameWeeks(peptideTimeFrame);
  
  const preferenceMap: { [key: string]: { min: number; max: number } } = {
    'immediate': { min: 0, max: 1 },
    'short-term': { min: 1, max: 4 },
    'medium-term': { min: 4, max: 12 },
    'long-term': { min: 8, max: 24 }
  };

  const range = preferenceMap[userPreference];
  if (!range) return 0;

  if (peptideWeeks >= range.min && peptideWeeks <= range.max) {
    return 10; // Perfect time match
  } else if (Math.abs(peptideWeeks - range.max) <= 2) {
    return 5; // Close time match
  }

  return 0;
};
