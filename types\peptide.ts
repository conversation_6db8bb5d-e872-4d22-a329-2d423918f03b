export interface Peptide {
  category: string;
  name: string;
  uses: string;
  effects: string;
  time_frame: string;
}

export interface QuizQuestion {
  id: string;
  question: string;
  options: QuizOption[];
  category?: string;
}

export interface QuizOption {
  id: string;
  text: string;
  keywords: string[];
  weight: number;
}

export interface QuizResponse {
  questionId: string;
  selectedOptionId: string;
  keywords: string[];
  weight: number;
}

export interface PeptideRecommendation {
  peptide: Peptide;
  score: number;
  matchedKeywords: string[];
  reasons: string[];
}

export interface QuizResult {
  recommendations: PeptideRecommendation[];
  userProfile: {
    primaryGoals: string[];
    timePreference: string;
    categories: string[];
  };
}
