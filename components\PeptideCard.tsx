'use client';

import { PeptideRecommendation } from '@/types/peptide';

interface PeptideCardProps {
  recommendation: PeptideRecommendation;
  rank: number;
}

export default function PeptideCard({ recommendation, rank }: PeptideCardProps) {
  const { peptide, score, reasons } = recommendation;

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-400 to-yellow-500 text-yellow-900';
      case 2:
        return 'bg-gradient-to-r from-gray-300 to-gray-400 text-gray-800';
      case 3:
        return 'bg-gradient-to-r from-orange-400 to-orange-500 text-orange-900';
      default:
        return 'bg-gradient-to-r from-blue-400 to-blue-500 text-blue-900';
    }
  };

  const getRankIcon = (rank: number) => {
    if (rank <= 3) {
      return '🏆';
    }
    return '⭐';
  };

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
      {/* Header with rank */}
      <div className={`${getRankColor(rank)} px-6 py-4`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="text-2xl mr-2">{getRankIcon(rank)}</span>
            <span className="font-bold text-lg">#{rank} Recommendation</span>
          </div>
          <div className="text-sm font-medium">
            Match Score: {Math.round(score)}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <h3 className="text-2xl font-bold text-gray-800 mb-2">
          {peptide.name}
        </h3>
        
        <div className="mb-4">
          <span className="inline-block bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
            {peptide.category}
          </span>
        </div>

        <div className="space-y-4">
          <div>
            <h4 className="font-semibold text-gray-700 mb-2">Primary Uses:</h4>
            <p className="text-gray-600 leading-relaxed">{peptide.uses}</p>
          </div>

          <div>
            <h4 className="font-semibold text-gray-700 mb-2">Expected Effects:</h4>
            <p className="text-gray-600 leading-relaxed">{peptide.effects}</p>
          </div>

          <div>
            <h4 className="font-semibold text-gray-700 mb-2">Timeline:</h4>
            <p className="text-gray-600 leading-relaxed">{peptide.time_frame}</p>
          </div>

          {reasons.length > 0 && (
            <div>
              <h4 className="font-semibold text-gray-700 mb-2">Why This Matches You:</h4>
              <ul className="space-y-1">
                {reasons.map((reason, index) => (
                  <li key={index} className="text-gray-600 flex items-start">
                    <span className="text-green-500 mr-2 mt-1">✓</span>
                    {reason}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="bg-gray-50 px-6 py-4 border-t">
        <p className="text-sm text-gray-500 text-center">
          Consult with a healthcare provider before starting any peptide therapy
        </p>
      </div>
    </div>
  );
}
