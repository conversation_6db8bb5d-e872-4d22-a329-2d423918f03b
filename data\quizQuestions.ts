import { QuizQuestion } from '@/types/peptide';

export const quizQuestions: QuizQuestion[] = [
  {
    id: 'primary-goal',
    question: 'What is your primary health goal?',
    options: [
      {
        id: 'weight-loss',
        text: 'Weight loss and metabolism improvement',
        keywords: ['weight', 'loss', 'metabolism', 'fat', 'appetite', 'obesity'],
        weight: 10
      },
      {
        id: 'muscle-performance',
        text: 'Muscle growth and athletic performance',
        keywords: ['muscle', 'growth', 'performance', 'strength', 'recovery', 'athletic'],
        weight: 10
      },
      {
        id: 'healing-recovery',
        text: 'Injury healing and recovery',
        keywords: ['healing', 'recovery', 'injury', 'inflammation', 'tissue', 'repair'],
        weight: 10
      },
      {
        id: 'cognitive-mood',
        text: 'Cognitive enhancement and mood support',
        keywords: ['cognitive', 'brain', 'memory', 'mood', 'focus', 'mental'],
        weight: 10
      },
      {
        id: 'longevity-energy',
        text: 'Anti-aging and energy enhancement',
        keywords: ['longevity', 'aging', 'energy', 'mitochondrial', 'cellular', 'nad'],
        weight: 10
      },
      {
        id: 'sleep-quality',
        text: 'Better sleep quality',
        keywords: ['sleep', 'rest', 'recovery', 'deep', 'quality'],
        weight: 10
      }
    ]
  },
  {
    id: 'specific-concerns',
    question: 'Which specific concerns do you want to address? (Select all that apply)',
    options: [
      {
        id: 'stubborn-fat',
        text: 'Stubborn fat areas',
        keywords: ['fat', 'stubborn', 'metabolism', 'visceral'],
        weight: 8
      },
      {
        id: 'joint-pain',
        text: 'Joint or muscle pain',
        keywords: ['joint', 'pain', 'muscle', 'inflammation', 'chronic'],
        weight: 8
      },
      {
        id: 'brain-fog',
        text: 'Brain fog or mental clarity issues',
        keywords: ['brain', 'fog', 'clarity', 'cognitive', 'mental'],
        weight: 8
      },
      {
        id: 'low-energy',
        text: 'Low energy or fatigue',
        keywords: ['energy', 'fatigue', 'tired', 'mitochondrial', 'cellular'],
        weight: 8
      },
      {
        id: 'poor-sleep',
        text: 'Poor sleep or insomnia',
        keywords: ['sleep', 'insomnia', 'rest', 'deep', 'quality'],
        weight: 8
      },
      {
        id: 'immune-issues',
        text: 'Immune system support',
        keywords: ['immune', 'immunity', 'infections', 'defense', 'viral'],
        weight: 8
      },
      {
        id: 'sexual-health',
        text: 'Sexual health or libido',
        keywords: ['sexual', 'libido', 'erectile', 'arousal', 'testosterone'],
        weight: 8
      }
    ]
  },
  {
    id: 'time-preference',
    question: 'How quickly would you like to see results?',
    options: [
      {
        id: 'immediate',
        text: 'Within days to 1 week',
        keywords: ['immediate', 'fast', 'quick'],
        weight: 5
      },
      {
        id: 'short-term',
        text: '1-4 weeks',
        keywords: ['short', 'weeks'],
        weight: 5
      },
      {
        id: 'medium-term',
        text: '1-3 months',
        keywords: ['medium', 'months'],
        weight: 5
      },
      {
        id: 'long-term',
        text: '3+ months (focused on long-term benefits)',
        keywords: ['long', 'sustained', 'months'],
        weight: 5
      }
    ]
  },
  {
    id: 'lifestyle',
    question: 'Which best describes your lifestyle?',
    options: [
      {
        id: 'very-active',
        text: 'Very active (daily intense exercise)',
        keywords: ['active', 'exercise', 'athletic', 'performance', 'training'],
        weight: 6
      },
      {
        id: 'moderately-active',
        text: 'Moderately active (regular exercise 3-5x/week)',
        keywords: ['moderate', 'regular', 'exercise', 'fitness'],
        weight: 6
      },
      {
        id: 'lightly-active',
        text: 'Lightly active (occasional exercise)',
        keywords: ['light', 'occasional', 'walking'],
        weight: 6
      },
      {
        id: 'sedentary',
        text: 'Mostly sedentary (desk job, minimal exercise)',
        keywords: ['sedentary', 'desk', 'minimal', 'metabolism'],
        weight: 6
      }
    ]
  },
  {
    id: 'health-focus',
    question: 'What aspect of health is most important to you right now?',
    options: [
      {
        id: 'physical-performance',
        text: 'Physical performance and strength',
        keywords: ['physical', 'performance', 'strength', 'muscle', 'athletic'],
        weight: 7
      },
      {
        id: 'mental-performance',
        text: 'Mental performance and cognitive function',
        keywords: ['mental', 'cognitive', 'brain', 'focus', 'memory'],
        weight: 7
      },
      {
        id: 'overall-wellness',
        text: 'Overall wellness and longevity',
        keywords: ['wellness', 'longevity', 'aging', 'health', 'vitality'],
        weight: 7
      },
      {
        id: 'recovery-healing',
        text: 'Recovery and healing from issues',
        keywords: ['recovery', 'healing', 'repair', 'inflammation', 'injury'],
        weight: 7
      }
    ]
  }
];
