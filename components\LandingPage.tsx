'use client';

interface LandingPageProps {
  onStartQuiz: () => void;
}

export default function LandingPage({ onStartQuiz }: LandingPageProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-4xl mx-auto text-center">
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h1 className="text-5xl font-bold text-gray-800 mb-4">
            🧬 Peptide Health Quiz
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            Discover the perfect peptides to optimize your health and wellness goals
          </p>
          
          <div className="grid md:grid-cols-3 gap-6 mb-8">
            <div className="p-6 bg-blue-50 rounded-lg">
              <div className="text-3xl mb-3">🎯</div>
              <h3 className="font-semibold text-gray-800 mb-2">Personalized</h3>
              <p className="text-gray-600 text-sm">
                Get recommendations tailored to your specific health goals and lifestyle
              </p>
            </div>
            
            <div className="p-6 bg-green-50 rounded-lg">
              <div className="text-3xl mb-3">🔬</div>
              <h3 className="font-semibold text-gray-800 mb-2">Science-Based</h3>
              <p className="text-gray-600 text-sm">
                Our recommendations are based on peptide research and proven benefits
              </p>
            </div>
            
            <div className="p-6 bg-purple-50 rounded-lg">
              <div className="text-3xl mb-3">⚡</div>
              <h3 className="font-semibold text-gray-800 mb-2">Fast Results</h3>
              <p className="text-gray-600 text-sm">
                Get your personalized peptide recommendations in just 2 minutes
              </p>
            </div>
          </div>

          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              What You'll Discover
            </h2>
            <div className="grid md:grid-cols-2 gap-4 text-left">
              <div className="flex items-start">
                <span className="text-green-500 mr-3 mt-1">✓</span>
                <span className="text-gray-700">Top 5 peptides matched to your goals</span>
              </div>
              <div className="flex items-start">
                <span className="text-green-500 mr-3 mt-1">✓</span>
                <span className="text-gray-700">Expected timeline for results</span>
              </div>
              <div className="flex items-start">
                <span className="text-green-500 mr-3 mt-1">✓</span>
                <span className="text-gray-700">Detailed benefits and effects</span>
              </div>
              <div className="flex items-start">
                <span className="text-green-500 mr-3 mt-1">✓</span>
                <span className="text-gray-700">Personalized match explanations</span>
              </div>
            </div>
          </div>

          <button
            onClick={onStartQuiz}
            className="bg-blue-500 text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-blue-600 transition-colors shadow-lg hover:shadow-xl transform hover:scale-105 transition-transform"
          >
            Start Your Peptide Quiz →
          </button>
          
          <p className="text-sm text-gray-500 mt-4">
            Takes 2-3 minutes • No email required • Instant results
          </p>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <p className="text-sm text-gray-500">
            <strong>Disclaimer:</strong> This quiz provides educational information only. 
            Always consult with a healthcare provider before starting any peptide therapy.
          </p>
        </div>
      </div>
    </div>
  );
}
