'use client';

import { useState } from 'react';
import { QuizResponse } from '@/types/peptide';
import { quizQuestions } from '@/data/quizQuestions';
import { generateRecommendations } from '@/utils/recommendationEngine';
import QuizQuestion from './QuizQuestion';
import ProgressBar from './ProgressBar';
import Results from './Results';
import LandingPage from './LandingPage';

export default function Quiz() {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [responses, setResponses] = useState<QuizResponse[]>([]);
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [showLanding, setShowLanding] = useState(true);

  const currentQuestion = quizQuestions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === quizQuestions.length - 1;
  const allowMultiple = currentQuestion.id === 'specific-concerns';

  const handleOptionSelect = (optionId: string) => {
    if (allowMultiple) {
      setSelectedOptions(prev => 
        prev.includes(optionId) 
          ? prev.filter(id => id !== optionId)
          : [...prev, optionId]
      );
    } else {
      setSelectedOptions([optionId]);
    }
  };

  const handleNext = () => {
    if (selectedOptions.length === 0) return;

    // Save responses for current question
    const newResponses = selectedOptions.map(optionId => {
      const option = currentQuestion.options.find(opt => opt.id === optionId);
      if (!option) throw new Error('Option not found');

      return {
        questionId: currentQuestion.id,
        selectedOptionId: optionId,
        keywords: option.keywords,
        weight: option.weight
      };
    });

    const updatedResponses = [...responses, ...newResponses];
    setResponses(updatedResponses);

    if (isLastQuestion) {
      setShowResults(true);
    } else {
      setCurrentQuestionIndex(prev => prev + 1);
      setSelectedOptions([]);
    }
  };

  const handleBack = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
      // Remove responses for the current question
      const filteredResponses = responses.filter(
        response => response.questionId !== currentQuestion.id
      );
      setResponses(filteredResponses);
      setSelectedOptions([]);
    }
  };

  const handleRestart = () => {
    setCurrentQuestionIndex(0);
    setResponses([]);
    setSelectedOptions([]);
    setShowResults(false);
    setShowLanding(true);
  };

  const handleStartQuiz = () => {
    setShowLanding(false);
  };

  if (showLanding) {
    return <LandingPage onStartQuiz={handleStartQuiz} />;
  }

  if (showResults) {
    const results = generateRecommendations(responses);
    return <Results results={results} onRestart={handleRestart} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            Peptide Health Quiz
          </h1>
          <p className="text-lg text-gray-600">
            Find the perfect peptide to optimize your health goals
          </p>
        </div>

        <ProgressBar 
          currentStep={currentQuestionIndex + 1} 
          totalSteps={quizQuestions.length} 
        />

        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <QuizQuestion
            question={currentQuestion}
            selectedOptions={selectedOptions}
            onOptionSelect={handleOptionSelect}
            allowMultiple={allowMultiple}
          />
        </div>

        <div className="flex justify-between max-w-2xl mx-auto">
          <button
            onClick={handleBack}
            disabled={currentQuestionIndex === 0}
            className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              currentQuestionIndex === 0
                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                : 'bg-gray-500 text-white hover:bg-gray-600'
            }`}
          >
            Back
          </button>

          <button
            onClick={handleNext}
            disabled={selectedOptions.length === 0}
            className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              selectedOptions.length === 0
                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                : 'bg-blue-500 text-white hover:bg-blue-600'
            }`}
          >
            {isLastQuestion ? 'Get My Recommendations' : 'Next'}
          </button>
        </div>
      </div>
    </div>
  );
}
