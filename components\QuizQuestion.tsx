'use client';

import { QuizQuestion as QuizQuestionType, QuizOption } from '@/types/peptide';

interface QuizQuestionProps {
  question: QuizQuestionType;
  selectedOptions: string[];
  onOptionSelect: (optionId: string) => void;
  allowMultiple?: boolean;
}

export default function QuizQuestion({ 
  question, 
  selectedOptions, 
  onOptionSelect,
  allowMultiple = false 
}: QuizQuestionProps) {
  const handleOptionClick = (optionId: string) => {
    onOptionSelect(optionId);
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-6">
      <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
        {question.question}
      </h2>
      
      <div className="space-y-3">
        {question.options.map((option: QuizOption) => (
          <button
            key={option.id}
            onClick={() => handleOptionClick(option.id)}
            className={`w-full p-4 text-left rounded-lg border-2 transition-all duration-200 hover:shadow-md ${
              selectedOptions.includes(option.id)
                ? 'border-blue-500 bg-blue-50 text-blue-800'
                : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center">
              <div className={`w-4 h-4 rounded-full border-2 mr-3 flex-shrink-0 ${
                selectedOptions.includes(option.id)
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-300'
              }`}>
                {selectedOptions.includes(option.id) && (
                  <div className="w-full h-full rounded-full bg-white scale-50"></div>
                )}
              </div>
              <span className="text-lg">{option.text}</span>
            </div>
          </button>
        ))}
      </div>
      
      {allowMultiple && (
        <p className="text-sm text-gray-500 mt-4 text-center">
          You can select multiple options
        </p>
      )}
    </div>
  );
}
