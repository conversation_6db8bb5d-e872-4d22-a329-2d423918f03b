'use client';

import { QuizResult } from '@/types/peptide';
import PeptideCard from './PeptideCard';

interface ResultsProps {
  results: QuizResult;
  onRestart: () => void;
}

export default function Results({ results, onRestart }: ResultsProps) {
  const { recommendations } = results;

  if (recommendations.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-4">
              No Recommendations Found
            </h1>
            <p className="text-lg text-gray-600 mb-6">
              We couldn't find peptides that match your specific criteria. 
              Please try taking the quiz again with different selections.
            </p>
            <button
              onClick={onRestart}
              className="bg-blue-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-600 transition-colors"
            >
              Retake Quiz
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            Your Personalized Peptide Recommendations
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            Based on your responses, here are the peptides that best match your health goals
          </p>
          <button
            onClick={onRestart}
            className="bg-gray-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-600 transition-colors"
          >
            Retake Quiz
          </button>
        </div>

        <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
          {recommendations.map((recommendation, index) => (
            <PeptideCard
              key={recommendation.peptide.name}
              recommendation={recommendation}
              rank={index + 1}
            />
          ))}
        </div>

        <div className="mt-12 bg-white rounded-xl shadow-lg p-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            Important Information
          </h2>
          <div className="prose text-gray-600">
            <p className="mb-4">
              <strong>Disclaimer:</strong> These recommendations are based on your quiz responses 
              and general peptide information. This is not medical advice.
            </p>
            <p className="mb-4">
              <strong>Next Steps:</strong> Consult with a healthcare provider or peptide specialist 
              before starting any peptide therapy. They can provide personalized dosing, 
              administration guidance, and monitor your progress.
            </p>
            <p>
              <strong>Quality Matters:</strong> Always source peptides from reputable, 
              tested suppliers to ensure purity and effectiveness.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
