# 🧬 Peptide Health Quiz App

A personalized peptide recommendation app built with Next.js, React, and TypeScript. This app helps users discover the best peptides for their health goals through an interactive quiz system.

## Features

- **Interactive Quiz**: 5-question quiz to understand user health goals and preferences
- **Smart Recommendations**: Algorithm that matches users to the top 5 most suitable peptides
- **Comprehensive Database**: 50+ peptides across 9 categories (Weight Loss, Performance, Healing, etc.)
- **Detailed Results**: Each recommendation includes uses, effects, timeline, and personalized reasons
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **No Registration**: Instant results without requiring email or account creation

## Categories Covered

- Weight Loss and Metabolism
- Growth Hormone/Body Composition/Performance
- Healing and Recovery
- Cognition & Mood
- Longevity/Mitochondrial Health/Energy
- Sleep
- Immunity
- Sexual Health
- Detox & Cosmetic

## Getting Started

1. **Install dependencies:**
```bash
npm install
```

2. **Run the development server:**
```bash
npm run dev
```

3. **Open your browser:**
Navigate to [http://localhost:3000](http://localhost:3000)

## Project Structure

```
├── app/                    # Next.js app directory
├── components/            # React components
│   ├── Quiz.tsx          # Main quiz orchestrator
│   ├── QuizQuestion.tsx  # Individual question component
│   ├── Results.tsx       # Results display
│   ├── PeptideCard.tsx   # Individual peptide recommendation
│   └── LandingPage.tsx   # Welcome screen
├── data/                 # Static data
│   └── quizQuestions.ts  # Quiz questions and options
├── types/                # TypeScript type definitions
│   └── peptide.ts        # Peptide and quiz interfaces
├── utils/                # Utility functions
│   ├── peptideData.ts    # Data processing utilities
│   └── recommendationEngine.ts # Recommendation algorithm
└── toptier_peptides.json # Peptide database
```

## How It Works

1. **User Journey**: Landing page → Quiz (5 questions) → Personalized results
2. **Recommendation Algorithm**:
   - Matches user keywords to peptide descriptions
   - Applies category bonuses for relevant selections
   - Considers time preference for results
   - Scores and ranks all peptides
3. **Results**: Top 5 peptides with match scores, reasons, and detailed information

## Customization

- **Add Peptides**: Update `toptier_peptides.json` with new peptide data
- **Modify Questions**: Edit `data/quizQuestions.ts` to change quiz flow
- **Adjust Algorithm**: Update `utils/recommendationEngine.ts` for different scoring
- **Styling**: Modify Tailwind classes throughout components

## Deployment

This app can be deployed as:
- **Web App**: Deploy to Vercel, Netlify, or any hosting platform
- **Native App**: Use with React Native or Capacitor for mobile apps

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: React useState hooks
- **Data**: JSON file (easily replaceable with database)

## Disclaimer

This application provides educational information only. Always consult with a healthcare provider before starting any peptide therapy.
